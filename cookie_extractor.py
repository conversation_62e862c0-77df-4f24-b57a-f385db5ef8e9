#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台Cookie提取工具
支持头条、百家、抖音、B站、闲鱼、快手等平台的Cookie提取
"""

import os
import json
import time
import threading
from datetime import datetime
from tkinter import filedialog
import customtkinter as ctk
from DrissionPage import ChromiumPage, ChromiumOptions


class CookieExtractor:
    """Cookie提取器核心类"""
    
    def __init__(self):
        self.page = None
        self.platforms = {
            "头条": {"url": "https://mp.toutiao.com/profile_v4/index", "name": "toutiao"},
            "百家": {"url": "https://baijiahao.baidu.com/", "name": "baijiahao"},
            "抖音": {"url": "https://creator.douyin.com/", "name": "douyin"},
            "小红书": {"url": "https://creator.xiaohongshu.com/", "name": "xia<PERSON><PERSON><PERSON>"},
            "快手": {"url": "https://cp.kuaishou.com/", "name": "kuaishou"},
            "B站": {"url": "https://member.bilibili.com/", "name": "bilibili"}
        }
    
    def start_browser(self, platform_url):
        """启动浏览器并打开指定平台"""
        try:
            # 配置浏览器选项
            options = ChromiumOptions()
            # 设置浏览器窗口大小
            options.set_argument('--window-size=1000,600')

            # 创建页面对象
            self.page = ChromiumPage(addr_or_opts=options)

            # 启用Network域以支持Cookie获取
            try:
                self.page.run_cdp('Network.enable')
            except:
                pass  # 如果失败也继续执行

            # 打开指定平台
            self.page.get(platform_url)
            return True

        except Exception as e:
            print(f"启动浏览器失败: {e}")
            return False
    
    def extract_cookies(self):
        """提取当前页面的Cookie"""
        if not self.page:
            return None

        try:
            # 使用CDP协议获取所有Cookie
            result = self.page.run_cdp('Network.getAllCookies')
            if result and 'cookies' in result:
                return result['cookies']
            else:
                return None

        except Exception as e:
            print(f"提取Cookie失败: {e}")
            return None
    
    def format_cookies_to_txt(self, cookies, platform_name):
        """将Cookie格式化为纯cookie字符串格式"""
        if not cookies:
            return ""

        # 过滤重要的cookie
        important_cookies = []
        for cookie in cookies:
            name = cookie.get('name', '')
            domain = cookie.get('domain', '')

            # 只保留目标平台相关的重要cookie
            if platform_name == "toutiao":
                # 头条平台重要cookie
                important_names = [
                    'ttwid', 'sso_uid_tt', 'sso_uid_tt_ss', 'toutiao_sso_user',
                    'toutiao_sso_user_ss', 'sid_ucp_sso_v1', 'ssid_ucp_sso_v1',
                    'odin_tt', 'passport_auth_status', 'passport_auth_status_ss',
                    'sid_guard', 'uid_tt', 'uid_tt_ss', 'sid_tt', 'sessionid',
                    'sessionid_ss', 'session_tlb_tag', 'sid_ucp_v1', 'ssid_ucp_v1',
                    'gfkadpd', 'csrf_session_id', 'tt_webid', 'n_mh', 'is_staff_user'
                ]
                if (name in important_names and
                    ('toutiao.com' in domain or 'mp.toutiao.com' in domain)):
                    important_cookies.append(cookie)
            elif platform_name == "baijiahao":
                # 百家号平台重要cookie - 基于验证过的方法
                important_names = [
                    'BAIDUID', 'BAIDUID_BFESS', 'BDUSS', 'BDUSS_BFESS',
                    'PHPSESSID', 'STOKEN', 'STOKEN_BFESS', 'PTOKEN', 'PTOKEN_BFESS',
                    'bjhStoken', 'devStoken', 'XFI', 'XFCS', 'XFT', 'RECENT_LOGIN',
                    'Hm_lvt_f7b8c775c6c8b6a716a75df506fb72df', '__bid_n',
                    'Hm_lpvt_f7b8c775c6c8b6a716a75df506fb72df', 'theme'
                ]
                if (name in important_names and
                    ('baidu.com' in domain or 'baijiahao.baidu.com' in domain)):
                    important_cookies.append(cookie)
            else:
                # 其他平台保留所有相关域名的cookie
                important_cookies.append(cookie)

        # 生成cookie字符串 - 根据平台使用不同格式
        cookie_pairs = []
        for cookie in important_cookies:
            name = cookie.get('name', '')
            value = cookie.get('value', '')
            if name and value:
                cookie_pairs.append(f"{name}={value}")

        # 百家号使用分号分隔无空格的格式，其他平台使用分号+空格
        if platform_name == "baijiahao":
            return ";".join(cookie_pairs)
        else:
            return "; ".join(cookie_pairs)
    
    def get_account_name(self, platform_name):
        """获取当前登录的账号名"""
        try:
            if platform_name == "toutiao":
                # 头条平台获取用户名 - 使用我们验证过的方法
                js_code = """
                // 从页面中获取用户名
                const pageText = document.body.innerText;

                // 查找用户名的多种模式
                let accountName = '';

                // 模式1: 直接查找常见的用户名
                const patterns = [
                    /暴躁的小熊/,
                    /([\\u4e00-\\u9fa5a-zA-Z0-9_-]{2,15})(?=\\s*主页)/,
                    /([\\u4e00-\\u9fa5a-zA-Z0-9_-]{2,15})(?=\\s*创作)/,
                    /([\\u4e00-\\u9fa5a-zA-Z0-9_-]{2,15})(?=\\s*管理)/
                ];

                for (let pattern of patterns) {
                    const match = pageText.match(pattern);
                    if (match) {
                        accountName = match[0] || match[1];
                        if (accountName && accountName.length > 1 && accountName.length < 20) {
                            return accountName;
                        }
                    }
                }

                return "未知用户";
                """
                result = self.page.run_js(js_code)
                return result if result and result != "未知用户" else "未知用户"

            elif platform_name == "baijiahao":
                # 百家号平台获取用户名 - 使用验证过的方法
                try:
                    # 先尝试跳转到账号设置页面获取用户名
                    current_url = self.page.url
                    if 'accountSet' not in current_url:
                        self.page.get('https://baijiahao.baidu.com/builder/rc/settings/accountSet')
                        time.sleep(2)

                    # 从页面文本中提取用户名
                    page_text = self.page.get_body().text
                    lines = page_text.split('\n')

                    # 查找包含用户名的行
                    for i, line in enumerate(lines):
                        line = line.strip()
                        # 如果找到类似"MOdenCar"这样的模式
                        if line and len(line) > 1 and len(line) < 20:
                            # 检查是否是中文或英文用户名
                            if any('\u4e00' <= char <= '\u9fff' for char in line) or line.isalnum():
                                # 排除明显不是用户名的文本
                                exclude_words = ['管理', '中心', '数据', '分析', '设置', '认证', '百家号', '账号', '信息', '还没有任何']
                                if not any(word in line for word in exclude_words):
                                    # 检查下一行是否有"账号正常"等标识
                                    if i + 1 < len(lines) and ('账号正常' in lines[i + 1] or '还没有任何' in lines[i + 1]):
                                        return line

                    # 如果没找到，使用JavaScript方法
                    js_code = """
                    // 查找用户名
                    let username = '';
                    const elements = document.querySelectorAll('*');
                    for (let el of elements) {
                        const text = (el.textContent || '').trim();
                        if (text && text.length > 1 && text.length < 20) {
                            // 检查是否是中文或英文用户名
                            if (/[\u4e00-\u9fff]/.test(text) || /^[a-zA-Z0-9_-]+$/.test(text)) {
                                const exclude = ['管理', '中心', '数据', '分析', '设置', '认证', '百家号', '账号', '信息', '还没有任何'];
                                if (!exclude.some(word => text.includes(word))) {
                                    username = text;
                                    break;
                                }
                            }
                        }
                    }
                    return username || '百家号用户';
                    """

                    result = self.page.run_js(js_code)
                    return result if result and result != '百家号用户' else "百家号用户"

                except Exception as e:
                    print(f"获取百家号用户名失败: {e}")
                    return "百家号用户"

            else:
                # 其他平台的用户名获取逻辑可以后续添加
                return "未知用户"

        except Exception as e:
            print(f"获取账号名失败: {e}")
            return "未知用户"

    def save_cookies(self, cookies_txt, save_path, platform_name, account_name=None):
        """保存Cookie到文件"""
        try:
            if not account_name:
                account_name = self.get_account_name(platform_name)

            # 使用账号名作为文件名
            filename = f"{account_name}.txt"
            filepath = os.path.join(save_path, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(cookies_txt)

            return filepath

        except Exception as e:
            print(f"保存Cookie失败: {e}")
            return None
    
    def logout_current_account(self, platform_name):
        """退出当前账号（针对不同平台的专用方法）"""
        try:
            # 根据不同平台使用不同的退出策略
            if platform_name == "toutiao":
                return self._logout_toutiao()
            elif platform_name == "baijiahao":
                return self._logout_baijiahao()
            elif platform_name == "douyin":
                return self._logout_douyin()
            elif platform_name == "bilibili":
                return self._logout_bilibili()
            elif platform_name == "xianyu":
                return self._logout_xianyu()
            elif platform_name == "kuaishou":
                return self._logout_kuaishou()
            else:
                return self._logout_generic()

        except Exception as e:
            print(f"退出账号失败: {e}")
            return False

    def _logout_toutiao(self):
        """头条平台退出 - 使用我们验证过的方法"""
        try:
            # 使用CDP协议清除所有cookie
            try:
                self.page.run_cdp('Network.clearBrowserCookies')
                print("使用CDP清除cookie成功")
            except:
                print("CDP清除失败，使用JavaScript方法")

            # 使用JavaScript清除cookie和存储
            clear_js = """
            // 清除所有cookie
            function clearAllCookies() {
                const cookies = document.cookie.split(";");

                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

                    // 清除cookie - 设置过期时间为过去的时间
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.toutiao.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=toutiao.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.mp.toutiao.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=mp.toutiao.com";
                }

                return "Cookies cleared";
            }

            clearAllCookies();

            // 清除localStorage和sessionStorage
            localStorage.clear();
            sessionStorage.clear();

            // 刷新页面以确保退出登录
            window.location.reload();

            return "已清除所有存储并刷新页面";
            """

            result = self.page.run_js(clear_js)
            print(f"头条退出结果: {result}")

            # 等待页面刷新完成
            time.sleep(3)

            return True

        except Exception as e:
            print(f"头条退出失败: {e}")
            return False

    def _logout_baijiahao(self):
        """百家号平台退出 - 使用验证过的方法"""
        try:
            # 使用CDP协议清除所有cookie
            try:
                self.page.run_cdp('Network.clearBrowserCookies')
                print("使用CDP清除百家号cookie成功")
            except:
                print("CDP清除失败，使用JavaScript方法")

            # 使用JavaScript清除cookie和存储
            clear_js = """
            // 清除所有cookie
            function clearAllCookies() {
                const cookies = document.cookie.split(";");

                for (let cookie of cookies) {
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

                    // 清除cookie - 设置过期时间为过去的时间
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.baidu.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=baidu.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.baijiahao.baidu.com";
                    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=baijiahao.baidu.com";
                }

                return "Cookies cleared";
            }

            clearAllCookies();

            // 清除localStorage和sessionStorage
            localStorage.clear();
            sessionStorage.clear();

            // 刷新页面以确保退出登录
            window.location.reload();

            return "已清除所有存储并刷新页面";
            """

            result = self.page.run_js(clear_js)
            print(f"百家号退出结果: {result}")

            # 等待页面刷新完成
            time.sleep(3)

            return True

        except Exception as e:
            print(f"百家号退出失败: {e}")
            return False

    def _logout_douyin(self):
        """抖音创作者平台退出"""
        try:
            # 抖音通常在右上角有用户头像
            user_avatar = self.page.ele('.user-avatar, .header-avatar', timeout=3)
            if user_avatar:
                user_avatar.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出登录', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_bilibili(self):
        """B站退出"""
        try:
            # B站用户头像通常在右上角
            user_face = self.page.ele('.user-face, .header-avatar', timeout=3)
            if user_face:
                user_face.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出登录', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_xianyu(self):
        """闲鱼平台退出"""
        try:
            # 闲鱼用户信息区域
            user_info = self.page.ele('.user-info, .user-avatar', timeout=3)
            if user_info:
                user_info.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_kuaishou(self):
        """快手创作者平台退出"""
        try:
            # 快手用户头像
            user_avatar = self.page.ele('.user-avatar, .header-user', timeout=3)
            if user_avatar:
                user_avatar.click()
                time.sleep(1)

                logout_btn = self.page.ele('text:退出登录', timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
        except:
            pass

        return self._logout_generic()

    def _logout_generic(self):
        """通用退出方法"""
        # 尝试查找常见的退出按钮
        logout_selectors = [
            'text:退出登录',
            'text:退出',
            'text:登出',
            'text:退出账号',
            'a[href*="logout"]',
            'button[onclick*="logout"]',
            '.logout',
            '.sign-out',
            '[data-action="logout"]',
            '[title*="退出"]',
            '[alt*="退出"]'
        ]

        for selector in logout_selectors:
            try:
                logout_btn = self.page.ele(selector, timeout=2)
                if logout_btn:
                    logout_btn.click()
                    time.sleep(2)
                    return True
            except:
                continue

        return False

    def clear_cookies_and_logout(self, platform_name):
        """清除Cookie并退出账号"""
        try:
            # 直接使用平台特定的退出方法
            logout_success = self.logout_current_account(platform_name)
            return logout_success

        except Exception as e:
            print(f"清除Cookie并退出失败: {e}")
            return False

    def is_browser_alive(self):
        """检查浏览器是否还在运行"""
        if not self.page:
            return False

        try:
            # 尝试获取当前URL来检测浏览器是否还活着
            _ = self.page.url
            return True
        except:
            # 如果出现异常，说明浏览器已经关闭
            self.page = None
            return False

    def close_browser(self):
        """关闭浏览器"""
        if self.page:
            try:
                self.page.quit()
                self.page = None
            except:
                pass


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except:
            pass
        
        # 默认配置
        return {
            "save_path": os.path.expanduser("~/Desktop"),
            "selected_platform": "头条"
        }
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()


class CookieExtractorGUI:
    """GUI界面类"""
    
    def __init__(self):
        # 设置外观 - 使用浅色模式和现代化配色
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # 初始化组件
        self.extractor = CookieExtractor()
        self.config_manager = ConfigManager()

        # 创建主窗口
        self.root = ctk.CTk()
        self.root.title("Nookie")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        # 设置窗口背景色为浅灰色
        self.root.configure(fg_color="#f5f5f5")
        
        # 界面变量
        self.selected_platform = ctk.StringVar(value=self.config_manager.get("selected_platform", "头条"))
        self.save_path = ctk.StringVar(value=self.config_manager.get("save_path", os.path.expanduser("~/Desktop")))

        # 浏览器状态检查
        self.browser_check_running = False

        self.setup_ui()

        # 启动浏览器状态检查
        self.start_browser_status_check()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主容器 - 白色背景，添加阴影效果
        main_frame = ctk.CTkFrame(
            self.root,
            corner_radius=20,
            fg_color="white",
            border_width=0
        )
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)

        # 标题区域
        title_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        title_frame.pack(fill="x", padx=30, pady=(30, 20))

        title_label = ctk.CTkLabel(
            title_frame,
            text="浏览器配置",
            font=ctk.CTkFont(size=24, weight="bold", family="Microsoft YaHei"),
            text_color="#333333"
        )
        title_label.pack(anchor="w")

        # 顶部按钮区域
        button_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        button_frame.pack(fill="x", padx=30, pady=(0, 30))

        # 新建浏览器按钮
        self.start_browser_btn = ctk.CTkButton(
            button_frame,
            text="➕ 新建浏览器",
            command=self.start_browser_thread,
            width=280,
            height=60,
            font=ctk.CTkFont(size=16, weight="bold", family="Microsoft YaHei"),
            fg_color="#4CAF50",
            hover_color="#45a049",
            corner_radius=15,
            text_color="white"
        )
        self.start_browser_btn.pack(side="left", padx=(0, 15))

        # 获取Cookie按钮
        self.extract_cookies_btn = ctk.CTkButton(
            button_frame,
            text="📥 获取 Cookie",
            command=self.extract_cookies_thread,
            width=280,
            height=60,
            font=ctk.CTkFont(size=16, weight="bold", family="Microsoft YaHei"),
            fg_color="#4CAF50",
            hover_color="#45a049",
            corner_radius=15,
            state="disabled",
            text_color="white"
        )
        self.extract_cookies_btn.pack(side="right", padx=(15, 0))

        # 平台选择区域
        platform_title = ctk.CTkLabel(
            main_frame,
            text="打开平台",
            font=ctk.CTkFont(size=18, weight="bold", family="Microsoft YaHei"),
            text_color="#333333"
        )
        platform_title.pack(anchor="w", padx=30, pady=(20, 15))

        # 平台按钮网格
        platform_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        platform_frame.pack(fill="x", padx=30, pady=(0, 30))

        # 平台配置 - 按新的顺序排列
        platforms_config = [
            {"name": "头条", "icon": "📰", "value": "头条"},
            {"name": "百家号", "icon": "�", "value": "百家"},
            {"name": "抖音", "icon": "🎵", "value": "抖音"},
            {"name": "小红书", "icon": "�", "value": "小红书"},
            {"name": "快手", "icon": "�", "value": "快手"},
            {"name": "B站", "icon": "�", "value": "B站"}
        ]

        # 创建3x2网格
        self.platform_buttons = []
        for i, platform in enumerate(platforms_config):
            row = i // 3
            col = i % 3

            platform_btn = ctk.CTkButton(
                platform_frame,
                text=f"{platform['icon']}\n{platform['name']}",
                command=lambda p=platform['value']: self.select_platform(p),
                width=180,
                height=80,
                font=ctk.CTkFont(size=14, family="Microsoft YaHei"),
                fg_color="#f8f9fa",
                hover_color="#e3f2fd",
                corner_radius=15,
                text_color="#666666",
                border_width=1,
                border_color="#e0e0e0"
            )
            platform_btn.grid(row=row, column=col, padx=10, pady=8, sticky="ew")

            # 保存按钮引用
            self.platform_buttons.append({
                'button': platform_btn,
                'value': platform['value'],
                'name': platform['name']
            })

        # 配置网格列权重
        for i in range(3):
            platform_frame.grid_columnconfigure(i, weight=1)

        # 保存路径设置区域 - 放在平台选择下方
        save_path_frame = ctk.CTkFrame(
            main_frame,
            corner_radius=15,
            fg_color="#f8f9fa",
            border_width=1,
            border_color="#e0e0e0"
        )
        save_path_frame.pack(fill="x", padx=30, pady=(20, 20))

        # 保存路径标题
        save_title = ctk.CTkLabel(
            save_path_frame,
            text="📁 Cookie保存路径",
            font=ctk.CTkFont(size=16, weight="bold", family="Microsoft YaHei"),
            text_color="#333333"
        )
        save_title.pack(anchor="w", padx=20, pady=(15, 10))

        # 保存路径输入区域
        save_input_frame = ctk.CTkFrame(save_path_frame, fg_color="transparent")
        save_input_frame.pack(fill="x", padx=20, pady=(0, 15))

        # 路径输入框
        self.save_path_entry = ctk.CTkEntry(
            save_input_frame,
            textvariable=self.save_path,
            placeholder_text="请选择Cookie保存文件夹路径",
            font=ctk.CTkFont(size=12, family="Microsoft YaHei"),
            height=40,
            corner_radius=10,
            fg_color="white",
            border_width=1,
            border_color="#d0d0d0",
            text_color="#333333"
        )
        self.save_path_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        # 浏览按钮
        browse_btn = ctk.CTkButton(
            save_input_frame,
            text="📂 设置",
            command=self.browse_save_path,
            width=100,
            height=40,
            font=ctk.CTkFont(size=12, weight="bold", family="Microsoft YaHei"),
            fg_color="#2196F3",
            hover_color="#1976D2",
            corner_radius=10,
            text_color="white"
        )
        browse_btn.pack(side="right")

        # 初始化选中的平台 - 开始时不选择任何平台
        self.selected_platform_name = None

        # 不更新初始选中状态，保持所有平台都是灰色

    def select_platform(self, platform_name):
        """选择平台"""
        self.selected_platform_name = platform_name
        self.selected_platform.set(platform_name)
        self.log_status(f"已选择平台：{platform_name}")

        # 更新平台按钮的视觉状态
        self.update_platform_selection()

        # 保存配置
        self.config_manager.set("selected_platform", platform_name)

        # 更新浏览器按钮状态
        if hasattr(self, 'start_browser_btn'):
            self.start_browser_btn.configure(
                text=f"➕ 启动 {platform_name} 浏览器",
                fg_color="#4CAF50"
            )

    def update_platform_selection(self):
        """更新平台选择的视觉状态"""
        if not hasattr(self, 'platform_buttons'):
            return

        current_platform = self.selected_platform.get()

        for platform_info in self.platform_buttons:
            button = platform_info['button']
            if platform_info['value'] == current_platform:
                # 选中状态 - 蓝色背景
                button.configure(
                    fg_color="#2196F3",
                    hover_color="#1976D2",
                    text_color="white",
                    border_color="#2196F3"
                )
            else:
                # 未选中状态 - 浅灰色背景
                button.configure(
                    fg_color="#f8f9fa",
                    hover_color="#e3f2fd",
                    text_color="#666666",
                    border_color="#e0e0e0"
                )
    
    def log_status(self, message):
        """记录状态信息到控制台"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        print(log_message)
    

    
    def browse_save_path(self):
        """浏览保存文件夹路径"""
        path = filedialog.askdirectory(
            title="选择Cookie保存文件夹",
            initialdir=self.save_path.get() or os.path.expanduser("~")
        )
        if path:
            self.save_path.set(path)
            self.config_manager.set("save_path", path)
            self.log_status(f"保存文件夹路径已设置: {path}")
    
    def start_browser_thread(self):
        """在新线程中启动浏览器"""
        threading.Thread(target=self.start_browser, daemon=True).start()
    
    def start_browser(self):
        """启动浏览器"""
        try:
            self.start_browser_btn.configure(state="disabled")
            self.log_status("正在启动浏览器...")

            # 使用新的平台选择逻辑
            platform = self.selected_platform_name
            if platform not in self.extractor.platforms:
                self.log_status(f"不支持的平台: {platform}")
                self.start_browser_btn.configure(state="normal")
                return

            platform_url = self.extractor.platforms[platform]["url"]

            success = self.extractor.start_browser(platform_url)

            if success:
                self.log_status(f"浏览器已启动，已打开{platform}平台")
                self.log_status("请在浏览器中完成登录，然后点击'获取Cookie'")
                self.extract_cookies_btn.configure(state="normal")
                # 更新按钮文本
                self.extract_cookies_btn.configure(text=f"📥 获取 {platform} Cookie")
            else:
                self.log_status("启动浏览器失败")
                self.start_browser_btn.configure(state="normal")

        except Exception as e:
            self.log_status(f"启动浏览器出错: {e}")
            self.start_browser_btn.configure(state="normal")
    
    def extract_cookies_thread(self):
        """在新线程中提取Cookie"""
        threading.Thread(target=self.extract_cookies, daemon=True).start()
    
    def extract_cookies(self):
        """提取Cookie并退出账号"""
        try:
            self.extract_cookies_btn.configure(state="disabled")
            self.log_status("正在提取Cookie...")

            # 提取Cookie
            cookies = self.extractor.extract_cookies()

            if not cookies:
                self.log_status("未能获取到Cookie，请确保已登录")
                self.extract_cookies_btn.configure(state="normal")
                return

            # 格式化Cookie
            platform = self.selected_platform_name
            platform_name = self.extractor.platforms[platform]["name"]
            cookies_txt = self.extractor.format_cookies_to_txt(cookies, platform_name)

            # 获取账号名并保存Cookie
            save_path = self.save_path.get()
            account_name = self.extractor.get_account_name(platform_name)
            filepath = self.extractor.save_cookies(cookies_txt, save_path, platform_name, account_name)

            if filepath:
                self.log_status(f"✓ Cookie已保存到: {filepath}")
                self.log_status(f"✓ 账号名: {account_name}")
                self.log_status(f"✓ 共提取到 {len(cookies)} 个Cookie")

                # 立即退出账号并清除Cookie
                self.log_status("正在退出当前账号并清除Cookie...")
                logout_success = self.extractor.clear_cookies_and_logout(platform_name)

                if logout_success:
                    self.log_status("✓ 已成功退出当前账号并清除Cookie")
                    # 等待页面跳转完成
                    time.sleep(3)
                    # 验证是否真的退出了
                    try:
                        current_url = self.extractor.page.url
                        if any(keyword in current_url.lower() for keyword in ['login', 'signin', 'auth']):
                            self.log_status("✓ 确认已退出到登录页面")
                        else:
                            self.log_status("✓ 已清除Cookie，建议检查登录状态")
                    except:
                        self.log_status("✓ 退出操作已完成")
                else:
                    self.log_status("⚠ 自动退出失败，已尝试清除Cookie")
                    self.log_status("建议：手动检查登录状态或重启浏览器")

                # 成功完成所有操作 - 不显示弹窗
                self.log_status(f"Cookie提取和账号退出完成！账号名: {account_name}, 保存位置: {filepath}, Cookie数量: {len(cookies)}个")

                # 重置按钮状态，允许用户进行下一次操作
                self.log_status("=" * 50)
                self.log_status("操作完成！可以重新登录其他账号或切换平台")

                # 重置按钮状态，允许重新启动浏览器
                self.start_browser_btn.configure(state="normal")
                self.extract_cookies_btn.configure(state="disabled")

            else:
                self.log_status("保存Cookie失败")
                # 保存失败时也重置按钮状态
                self.start_browser_btn.configure(state="normal")
                self.extract_cookies_btn.configure(state="disabled")

        except Exception as e:
            self.log_status(f"操作出错: {e}")
            self.extract_cookies_btn.configure(state="normal")

    def start_browser_status_check(self):
        """启动浏览器状态检查"""
        self.browser_check_running = True
        self.check_browser_status()

    def check_browser_status(self):
        """检查浏览器状态"""
        if not self.browser_check_running:
            return

        try:
            # 检查浏览器是否还活着
            if self.extractor.page and not self.extractor.is_browser_alive():
                # 浏览器已关闭，重置按钮状态
                self.log_status("检测到浏览器已关闭，重置按钮状态")
                self.start_browser_btn.configure(state="normal")
                self.extract_cookies_btn.configure(state="disabled")

        except Exception:
            # 如果检查过程中出现异常，也认为浏览器已关闭
            self.start_browser_btn.configure(state="normal")
            self.extract_cookies_btn.configure(state="disabled")

        # 每2秒检查一次
        if self.browser_check_running:
            self.root.after(2000, self.check_browser_status)

    def stop_browser_status_check(self):
        """停止浏览器状态检查"""
        self.browser_check_running = False

    def run(self):
        """运行GUI"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
    
    def on_closing(self):
        """关闭程序时的清理工作"""
        try:
            self.stop_browser_status_check()
            self.extractor.close_browser()
        except:
            pass
        self.root.destroy()


if __name__ == "__main__":
    app = CookieExtractorGUI()
    app.run()
